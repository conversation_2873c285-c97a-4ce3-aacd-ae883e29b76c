// Admin Panel Management System
class AdminPanel {
    constructor() {
        this.gameData = null;
        this.currentEditingLevel = null;
        this.currentEditingWord = null;
        this.currentEditingWordIndex = null;
        
        this.initializeAdmin();
    }

    async initializeAdmin() {
        try {
            // Load game data
            const response = await fetch('data/words.json');
            this.gameData = await response.json();
            
            // Initialize UI
            this.initializeUI();
            this.renderLevels();
            this.populateLevelSelect();
            
            // Hide loading
            this.hideLoading();
            
        } catch (error) {
            console.error('Failed to load game data:', error);
            this.showMessage('Failed to load game data. Please check if the data file exists.', 'error');
        }
    }

    initializeUI() {
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Level management
        document.getElementById('add-level-btn').addEventListener('click', () => this.showLevelModal());
        document.getElementById('save-level').addEventListener('click', () => this.saveLevel());
        document.getElementById('cancel-level').addEventListener('click', () => this.hideLevelModal());
        document.getElementById('close-level-modal').addEventListener('click', () => this.hideLevelModal());

        // Word management
        document.getElementById('add-word-btn').addEventListener('click', () => this.showWordModal());
        document.getElementById('level-select').addEventListener('change', (e) => this.renderWords(parseInt(e.target.value)));
        document.getElementById('save-word').addEventListener('click', () => this.saveWord());
        document.getElementById('cancel-word').addEventListener('click', () => this.hideWordModal());
        document.getElementById('close-word-modal').addEventListener('click', () => this.hideWordModal());

        // Import/Export
        document.getElementById('export-btn').addEventListener('click', () => this.exportData());
        document.getElementById('import-btn').addEventListener('click', () => this.importData());
        document.getElementById('import-file').addEventListener('change', (e) => this.handleFileImport(e));

        // Save all changes
        document.getElementById('save-all-btn').addEventListener('click', () => this.saveAllChanges());

        // Confirmation modal
        document.getElementById('confirm-cancel').addEventListener('click', () => this.hideConfirmModal());

        // Auto-generate scrambled letters and hints
        document.getElementById('word-text').addEventListener('input', (e) => {
            this.autoGenerateWordData(e.target.value);
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // Load specific tab data
        if (tabName === 'words') {
            const selectedLevel = document.getElementById('level-select').value;
            if (selectedLevel) {
                this.renderWords(parseInt(selectedLevel));
            }
        }
    }

    renderLevels() {
        const container = document.getElementById('levels-grid');
        container.innerHTML = '';

        this.gameData.levels.forEach(level => {
            const levelCard = document.createElement('div');
            levelCard.className = 'level-card';
            levelCard.innerHTML = `
                <div class="card-header">
                    <div class="card-title">Level ${level.level}: ${level.title}</div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-small" onclick="adminPanel.editLevel(${level.level})">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-danger btn-small" onclick="adminPanel.deleteLevel(${level.level})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <p><strong>Words:</strong> ${level.words.length}</p>
                    <p><strong>Total Points:</strong> ${level.words.reduce((sum, word) => sum + word.points, 0)}</p>
                    <div class="word-preview">
                        <strong>Sample Words:</strong> ${level.words.slice(0, 3).map(w => w.word).join(', ')}
                        ${level.words.length > 3 ? '...' : ''}
                    </div>
                </div>
            `;
            container.appendChild(levelCard);
        });
    }

    populateLevelSelect() {
        const select = document.getElementById('level-select');
        select.innerHTML = '<option value="">Select a level...</option>';
        
        this.gameData.levels.forEach(level => {
            const option = document.createElement('option');
            option.value = level.level;
            option.textContent = `Level ${level.level}: ${level.title}`;
            select.appendChild(option);
        });
    }

    renderWords(levelNumber) {
        const container = document.getElementById('words-grid');
        container.innerHTML = '';

        const level = this.gameData.levels.find(l => l.level === levelNumber);
        if (!level) return;

        level.words.forEach((word, index) => {
            const wordCard = document.createElement('div');
            wordCard.className = 'word-card';
            wordCard.innerHTML = `
                <div class="card-header">
                    <div class="card-title">${word.word}</div>
                    <div class="card-actions">
                        <button class="btn btn-primary btn-small" onclick="adminPanel.editWord(${levelNumber}, ${index})">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-danger btn-small" onclick="adminPanel.deleteWord(${levelNumber}, ${index})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="word-preview">
                        <p><strong>Meaning:</strong> ${word.meaning.substring(0, 100)}${word.meaning.length > 100 ? '...' : ''}</p>
                        <p><strong>Hints:</strong> ${word.hints.join(' ')}</p>
                        <p><strong>Scrambled:</strong> ${word.scrambled.join(', ')}</p>
                    </div>
                    <div class="word-stats">
                        <span>Points: ${word.points}</span>
                        <span>Length: ${word.word.length} letters</span>
                    </div>
                </div>
            `;
            container.appendChild(wordCard);
        });
    }

    showLevelModal(level = null) {
        this.currentEditingLevel = level;
        const modal = document.getElementById('level-modal');
        const title = document.getElementById('level-modal-title');
        
        if (level) {
            title.textContent = 'Edit Level';
            document.getElementById('level-number').value = level.level;
            document.getElementById('level-title').value = level.title;
        } else {
            title.textContent = 'Add New Level';
            document.getElementById('level-number').value = this.gameData.levels.length + 1;
            document.getElementById('level-title').value = '';
        }
        
        modal.classList.add('active');
    }

    hideLevelModal() {
        document.getElementById('level-modal').classList.remove('active');
        this.currentEditingLevel = null;
    }

    saveLevel() {
        const levelNumber = parseInt(document.getElementById('level-number').value);
        const levelTitle = document.getElementById('level-title').value.trim();

        if (!levelTitle) {
            this.showMessage('Please enter a level title.', 'error');
            return;
        }

        if (this.currentEditingLevel) {
            // Edit existing level
            const level = this.gameData.levels.find(l => l.level === this.currentEditingLevel.level);
            level.level = levelNumber;
            level.title = levelTitle;
        } else {
            // Add new level
            if (this.gameData.levels.find(l => l.level === levelNumber)) {
                this.showMessage('A level with this number already exists.', 'error');
                return;
            }
            
            this.gameData.levels.push({
                level: levelNumber,
                title: levelTitle,
                words: []
            });
        }

        // Sort levels by number
        this.gameData.levels.sort((a, b) => a.level - b.level);

        this.renderLevels();
        this.populateLevelSelect();
        this.hideLevelModal();
        this.showMessage('Level saved successfully!', 'success');
    }

    editLevel(levelNumber) {
        const level = this.gameData.levels.find(l => l.level === levelNumber);
        this.showLevelModal(level);
    }

    deleteLevel(levelNumber) {
        this.showConfirmModal(
            `Are you sure you want to delete Level ${levelNumber}? This will also delete all words in this level.`,
            () => {
                this.gameData.levels = this.gameData.levels.filter(l => l.level !== levelNumber);
                this.renderLevels();
                this.populateLevelSelect();
                this.showMessage('Level deleted successfully!', 'success');
            }
        );
    }

    showWordModal(level = null, wordIndex = null) {
        const modal = document.getElementById('word-modal');
        const title = document.getElementById('word-modal-title');
        
        if (level && wordIndex !== null) {
            // Edit existing word
            const word = level.words[wordIndex];
            title.textContent = 'Edit Word';
            document.getElementById('word-text').value = word.word;
            document.getElementById('word-points').value = word.points;
            document.getElementById('word-scrambled').value = word.scrambled.join(',');
            document.getElementById('word-hints').value = word.hints.join(',');
            document.getElementById('word-meaning').value = word.meaning;
            document.getElementById('word-tips').value = word.tips;
            document.getElementById('word-fun-fact').value = word.didYouKnow;
            this.currentEditingWord = level;
            this.currentEditingWordIndex = wordIndex;
        } else {
            // Add new word
            title.textContent = 'Add New Word';
            document.getElementById('word-form').reset();
            document.getElementById('word-points').value = 10;
            this.currentEditingWord = null;
            this.currentEditingWordIndex = null;
        }
        
        modal.classList.add('active');
    }

    hideWordModal() {
        document.getElementById('word-modal').classList.remove('active');
        this.currentEditingWord = null;
        this.currentEditingWordIndex = null;
    }

    autoGenerateWordData(word) {
        if (!word) return;
        
        word = word.toUpperCase();
        const letters = word.split('');
        
        // Generate scrambled letters (shuffle the word)
        const scrambled = [...letters].sort(() => Math.random() - 0.5);
        document.getElementById('word-scrambled').value = scrambled.join(',');
        
        // Generate hints pattern (show first and last letter, rest as blanks)
        const hints = letters.map((letter, index) => {
            if (index === 0 || index === letters.length - 1) {
                return letter;
            }
            return '_';
        });
        document.getElementById('word-hints').value = hints.join(',');
    }

    saveWord() {
        const selectedLevel = document.getElementById('level-select').value;
        if (!selectedLevel && !this.currentEditingWord) {
            this.showMessage('Please select a level first.', 'error');
            return;
        }

        const wordData = {
            word: document.getElementById('word-text').value.toUpperCase().trim(),
            points: parseInt(document.getElementById('word-points').value),
            scrambled: document.getElementById('word-scrambled').value.split(',').map(s => s.trim().toUpperCase()),
            hints: document.getElementById('word-hints').value.split(',').map(s => s.trim().toUpperCase()),
            meaning: document.getElementById('word-meaning').value.trim(),
            tips: document.getElementById('word-tips').value.trim(),
            didYouKnow: document.getElementById('word-fun-fact').value.trim()
        };

        // Validation
        if (!wordData.word || !wordData.meaning || !wordData.tips || !wordData.didYouKnow) {
            this.showMessage('Please fill in all required fields.', 'error');
            return;
        }

        if (wordData.hints.length !== wordData.word.length) {
            this.showMessage('Hints pattern must match the word length.', 'error');
            return;
        }

        if (this.currentEditingWord && this.currentEditingWordIndex !== null) {
            // Edit existing word
            this.currentEditingWord.words[this.currentEditingWordIndex] = wordData;
        } else {
            // Add new word
            const level = this.gameData.levels.find(l => l.level === parseInt(selectedLevel));
            level.words.push(wordData);
        }

        this.renderWords(this.currentEditingWord ? this.currentEditingWord.level : parseInt(selectedLevel));
        this.hideWordModal();
        this.showMessage('Word saved successfully!', 'success');
    }

    editWord(levelNumber, wordIndex) {
        const level = this.gameData.levels.find(l => l.level === levelNumber);
        this.showWordModal(level, wordIndex);
    }

    deleteWord(levelNumber, wordIndex) {
        this.showConfirmModal(
            'Are you sure you want to delete this word?',
            () => {
                const level = this.gameData.levels.find(l => l.level === levelNumber);
                level.words.splice(wordIndex, 1);
                this.renderWords(levelNumber);
                this.showMessage('Word deleted successfully!', 'success');
            }
        );
    }

    exportData() {
        const dataStr = JSON.stringify(this.gameData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'anagram-game-data.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        this.showMessage('Game data exported successfully!', 'success');
    }

    importData() {
        document.getElementById('import-file').click();
    }

    handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);
                
                // Basic validation
                if (!importedData.levels || !Array.isArray(importedData.levels)) {
                    throw new Error('Invalid data format');
                }

                this.showConfirmModal(
                    'This will replace all current game data. Are you sure you want to continue?',
                    () => {
                        this.gameData = importedData;
                        this.renderLevels();
                        this.populateLevelSelect();
                        this.showMessage('Game data imported successfully!', 'success');
                    }
                );
            } catch (error) {
                this.showMessage('Failed to import data. Please check the file format.', 'error');
            }
        };
        reader.readAsText(file);
    }

    async saveAllChanges() {
        this.showLoading();
        
        try {
            // In a real application, you would send this data to a server
            // For this demo, we'll simulate saving to localStorage
            localStorage.setItem('anagramGameData', JSON.stringify(this.gameData));
            
            // Simulate network delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            this.hideLoading();
            this.showMessage('All changes saved successfully!', 'success');
        } catch (error) {
            this.hideLoading();
            this.showMessage('Failed to save changes. Please try again.', 'error');
        }
    }

    showConfirmModal(message, onConfirm) {
        document.getElementById('confirm-message').textContent = message;
        document.getElementById('confirm-modal').classList.add('active');
        
        const confirmBtn = document.getElementById('confirm-ok');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        newConfirmBtn.addEventListener('click', () => {
            this.hideConfirmModal();
            onConfirm();
        });
    }

    hideConfirmModal() {
        document.getElementById('confirm-modal').classList.remove('active');
    }

    showLoading() {
        document.getElementById('loading-overlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loading-overlay').classList.remove('active');
    }

    showMessage(message, type = 'success') {
        const container = document.getElementById('message-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        
        container.appendChild(messageDiv);
        
        setTimeout(() => {
            if (container.contains(messageDiv)) {
                container.removeChild(messageDiv);
            }
        }, 5000);
    }
}

// Initialize admin panel when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});
