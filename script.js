// Game State Management
class AnagramGame {
    constructor() {
        this.gameData = null;
        this.currentLevel = 1;
        this.currentWordIndex = 0;
        this.currentWord = null;
        this.playerAnswer = [];
        this.score = 0;
        this.soundEnabled = true;
        this.gameProgress = this.loadProgress();
        
        this.initializeGame();
    }

    async initializeGame() {
        try {
            // Load game data
            const response = await fetch('data/words.json');
            this.gameData = await response.json();
            
            // Initialize UI
            this.initializeUI();
            this.loadCurrentWord();
            this.updateDisplay();
            
            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loading-screen').classList.add('hidden');
            }, 1000);
            
        } catch (error) {
            console.error('Failed to load game data:', error);
            alert('Failed to load game. Please refresh the page.');
        }
    }

    initializeUI() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchPage(e.target.closest('.nav-btn').dataset.page);
            });
        });

        // Game controls
        document.getElementById('hint-btn').addEventListener('click', () => this.showHint());
        document.getElementById('reveal-btn').addEventListener('click', () => this.revealAnswer());
        document.getElementById('reset-btn').addEventListener('click', () => this.resetCurrentWord());

        // Settings
        document.getElementById('sound-toggle').addEventListener('change', (e) => {
            this.soundEnabled = e.target.checked;
            this.saveSettings();
        });

        // Modal controls
        document.getElementById('next-word-btn').addEventListener('click', () => this.nextWord());
        document.getElementById('practice-btn').addEventListener('click', () => this.checkPractice());

        // Load settings
        this.loadSettings();
    }

    loadCurrentWord() {
        const levelData = this.gameData.levels.find(l => l.level === this.currentLevel);
        if (!levelData || this.currentWordIndex >= levelData.words.length) {
            this.nextLevel();
            return;
        }

        this.currentWord = levelData.words[this.currentWordIndex];
        this.playerAnswer = new Array(this.currentWord.word.length).fill('');
        this.renderWordBoxes();
        this.renderScrambledLetters();
        this.updateWordHint();
    }

    renderWordBoxes() {
        const container = document.getElementById('word-boxes');
        container.innerHTML = '';

        this.currentWord.word.split('').forEach((letter, index) => {
            const box = document.createElement('div');
            box.className = 'word-box';
            box.dataset.index = index;

            // Check if this position has a hint
            if (this.currentWord.hints[index] !== '_') {
                box.textContent = this.currentWord.hints[index];
                box.classList.add('hint');
                this.playerAnswer[index] = this.currentWord.hints[index];
            }

            container.appendChild(box);
        });
    }

    renderScrambledLetters() {
        const container = document.getElementById('scrambled-letters');
        container.innerHTML = '';

        // Create available letters (excluding hint letters)
        const availableLetters = [...this.currentWord.scrambled];
        
        // Remove hint letters from available letters
        this.currentWord.hints.forEach(hint => {
            if (hint !== '_') {
                const index = availableLetters.indexOf(hint);
                if (index > -1) {
                    availableLetters.splice(index, 1);
                }
            }
        });

        availableLetters.forEach((letter, index) => {
            const box = document.createElement('div');
            box.className = 'letter-box';
            box.textContent = letter;
            box.dataset.letter = letter;
            box.dataset.originalIndex = index;
            
            box.addEventListener('click', () => this.selectLetter(box));
            container.appendChild(box);
        });
    }

    selectLetter(letterBox) {
        if (letterBox.classList.contains('used')) return;

        const letter = letterBox.dataset.letter;
        const nextEmptyIndex = this.getNextEmptyPosition();

        if (nextEmptyIndex === -1) return; // Word is complete

        // Check if this is the correct letter for this position
        const correctLetter = this.currentWord.word[nextEmptyIndex];
        
        if (letter === correctLetter) {
            // Correct letter
            this.playSound('correct');
            this.animateLetterToPosition(letterBox, nextEmptyIndex);
            this.playerAnswer[nextEmptyIndex] = letter;
            letterBox.classList.add('used');
            
            // Check if word is complete
            if (this.isWordComplete()) {
                setTimeout(() => this.completeWord(), 500);
            }
        } else {
            // Wrong letter
            this.playSound('wrong');
            this.animateWrongSelection(letterBox);
        }
    }

    animateLetterToPosition(letterBox, targetIndex) {
        const targetBox = document.querySelector(`[data-index="${targetIndex}"]`);
        const letterRect = letterBox.getBoundingClientRect();
        const targetRect = targetBox.getBoundingClientRect();

        // Create animated letter
        const animatedLetter = letterBox.cloneNode(true);
        animatedLetter.classList.add('moving');
        animatedLetter.style.position = 'fixed';
        animatedLetter.style.left = letterRect.left + 'px';
        animatedLetter.style.top = letterRect.top + 'px';
        animatedLetter.style.zIndex = '1000';
        
        document.body.appendChild(animatedLetter);

        // Animate to target position
        animatedLetter.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        animatedLetter.style.left = targetRect.left + 'px';
        animatedLetter.style.top = targetRect.top + 'px';

        setTimeout(() => {
            targetBox.textContent = letterBox.textContent;
            targetBox.classList.add('filled', 'correct-animation');
            document.body.removeChild(animatedLetter);
            
            setTimeout(() => {
                targetBox.classList.remove('correct-animation');
            }, 600);
        }, 500);
    }

    animateWrongSelection(letterBox) {
        letterBox.classList.add('wrong-animation');
        setTimeout(() => {
            letterBox.classList.remove('wrong-animation');
        }, 600);
    }

    getNextEmptyPosition() {
        for (let i = 0; i < this.playerAnswer.length; i++) {
            if (this.playerAnswer[i] === '') {
                return i;
            }
        }
        return -1;
    }

    isWordComplete() {
        return this.playerAnswer.every(letter => letter !== '');
    }

    completeWord() {
        this.playSound('victory');
        this.score += this.currentWord.points;
        this.updateDisplay();
        this.showConfetti();
        
        // Update progress
        this.updateProgress();
        
        // Show learning modal
        setTimeout(() => {
            this.showLearningModal();
        }, 1000);
    }

    showLearningModal() {
        const modal = document.getElementById('learning-modal');
        const word = this.currentWord;

        // Populate modal content
        document.getElementById('learned-word').textContent = word.word;
        document.getElementById('word-pronunciation').textContent = `/${word.word.toLowerCase()}/`;
        document.getElementById('word-meaning-detail').textContent = word.meaning;
        document.getElementById('word-tips').textContent = word.tips;
        document.getElementById('word-fun-fact').textContent = word.didYouKnow;

        // Create practice sentence
        this.createPracticeSentence();

        modal.classList.add('active');
    }

    createPracticeSentence() {
        const sentences = [
            `The word "${this.currentWord.word.toLowerCase()}" is very _____ in English.`,
            `I learned that "${this.currentWord.word.toLowerCase()}" means _____.`,
            `Can you use "${this.currentWord.word.toLowerCase()}" in a _____?`,
        ];

        const answers = ['useful', 'something important', 'sentence'];
        const randomIndex = Math.floor(Math.random() * sentences.length);

        document.getElementById('practice-sentence').innerHTML = sentences[randomIndex];
        document.getElementById('practice-btn').disabled = false;
    }

    checkPractice() {
        // Simple practice check - just enable next button
        document.getElementById('practice-btn').textContent = 'Great job! ✓';
        document.getElementById('practice-btn').disabled = true;
        document.getElementById('next-word-btn').disabled = false;
    }

    nextWord() {
        document.getElementById('learning-modal').classList.remove('active');
        this.currentWordIndex++;
        
        if (this.currentWordIndex >= this.gameData.levels.find(l => l.level === this.currentLevel).words.length) {
            this.nextLevel();
        } else {
            this.loadCurrentWord();
            this.updateDisplay();
        }
    }

    nextLevel() {
        this.currentLevel++;
        this.currentWordIndex = 0;
        
        if (this.currentLevel > this.gameData.levels.length) {
            this.showGameComplete();
            return;
        }
        
        this.loadCurrentWord();
        this.updateDisplay();
    }

    showHint() {
        if (this.score < 5) {
            alert('Not enough points for a hint! You need 5 points.');
            return;
        }

        this.score -= 5;
        this.updateDisplay();

        // Show next correct letter position
        const nextEmpty = this.getNextEmptyPosition();
        if (nextEmpty !== -1) {
            const correctLetter = this.currentWord.word[nextEmpty];
            const letterBoxes = document.querySelectorAll('.letter-box:not(.used)');
            
            letterBoxes.forEach(box => {
                if (box.dataset.letter === correctLetter) {
                    box.style.background = 'linear-gradient(135deg, #ffeaa7, #fdcb6e)';
                    box.style.transform = 'scale(1.1)';
                    
                    setTimeout(() => {
                        box.style.background = '';
                        box.style.transform = '';
                    }, 2000);
                    return;
                }
            });
        }
    }

    revealAnswer() {
        if (this.score < 10) {
            alert('Not enough points to reveal! You need 10 points.');
            return;
        }

        this.score -= 10;
        this.updateDisplay();

        // Fill in the complete word
        this.currentWord.word.split('').forEach((letter, index) => {
            if (this.playerAnswer[index] === '') {
                this.playerAnswer[index] = letter;
                const targetBox = document.querySelector(`[data-index="${index}"]`);
                targetBox.textContent = letter;
                targetBox.classList.add('filled');
            }
        });

        // Mark all scrambled letters as used
        document.querySelectorAll('.letter-box').forEach(box => {
            box.classList.add('used');
        });

        setTimeout(() => {
            this.completeWord();
        }, 1000);
    }

    resetCurrentWord() {
        this.playerAnswer = new Array(this.currentWord.word.length).fill('');
        
        // Reset hint letters
        this.currentWord.hints.forEach((hint, index) => {
            if (hint !== '_') {
                this.playerAnswer[index] = hint;
            }
        });
        
        this.renderWordBoxes();
        this.renderScrambledLetters();
    }

    playSound(type) {
        if (!this.soundEnabled) return;
        
        const audio = document.getElementById(`${type}-sound`);
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(e => console.log('Audio play failed:', e));
        }
    }

    showConfetti() {
        const container = document.getElementById('confetti-container');
        const colors = ['#667eea', '#764ba2', '#ffeaa7', '#fd79a8', '#74b9ff', '#00b894'];
        
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.animationDelay = Math.random() * 2 + 's';
            confetti.style.animationDuration = (Math.random() * 2 + 2) + 's';
            
            container.appendChild(confetti);
            
            setTimeout(() => {
                if (container.contains(confetti)) {
                    container.removeChild(confetti);
                }
            }, 4000);
        }
    }

    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.currentLevel;
        document.getElementById('progress').textContent = `${this.currentWordIndex + 1}/5`;
    }

    updateWordHint() {
        document.getElementById('word-meaning').textContent = this.currentWord.meaning;
    }

    switchPage(page) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        // Update pages
        document.querySelectorAll('.game-page').forEach(p => {
            p.classList.remove('active');
        });
        document.getElementById(`${page}-page`).classList.add('active');

        if (page === 'progress') {
            this.renderProgressPage();
        }
    }

    renderProgressPage() {
        const container = document.getElementById('level-progress');
        container.innerHTML = '';

        this.gameData.levels.forEach(level => {
            const card = document.createElement('div');
            card.className = 'level-card';
            
            const isUnlocked = level.level <= this.currentLevel;
            const progress = this.gameProgress[level.level] || { completed: 0, total: level.words.length };
            const isCompleted = progress.completed === progress.total;

            if (!isUnlocked) card.classList.add('locked');
            if (isCompleted) card.classList.add('completed');

            card.innerHTML = `
                <div class="level-title">Level ${level.level}: ${level.title}</div>
                <div class="level-stats">
                    <span>Words: ${progress.completed}/${progress.total}</span>
                    <span>${isCompleted ? 'Complete!' : isUnlocked ? 'In Progress' : 'Locked'}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(progress.completed / progress.total) * 100}%"></div>
                </div>
            `;

            container.appendChild(card);
        });
    }

    updateProgress() {
        if (!this.gameProgress[this.currentLevel]) {
            this.gameProgress[this.currentLevel] = { completed: 0, total: 5 };
        }
        
        this.gameProgress[this.currentLevel].completed = Math.max(
            this.gameProgress[this.currentLevel].completed,
            this.currentWordIndex + 1
        );
        
        this.saveProgress();
    }

    saveProgress() {
        localStorage.setItem('anagramGameProgress', JSON.stringify(this.gameProgress));
    }

    loadProgress() {
        const saved = localStorage.getItem('anagramGameProgress');
        return saved ? JSON.parse(saved) : {};
    }

    saveSettings() {
        const settings = {
            soundEnabled: this.soundEnabled
        };
        localStorage.setItem('anagramGameSettings', JSON.stringify(settings));
    }

    loadSettings() {
        const saved = localStorage.getItem('anagramGameSettings');
        if (saved) {
            const settings = JSON.parse(saved);
            this.soundEnabled = settings.soundEnabled;
            document.getElementById('sound-toggle').checked = this.soundEnabled;
        }
    }

    showGameComplete() {
        alert('Congratulations! You have completed all levels! 🎉');
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game = new AnagramGame();
});
